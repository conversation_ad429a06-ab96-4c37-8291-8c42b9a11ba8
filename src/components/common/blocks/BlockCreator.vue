<template>
  <q-page>
    <draggable
      v-model="draggableBlocks"
      :component-data="{
        tag: 'div',
        type: 'transition-group',
        name: !isDragging ? 'flip-list' : null,
      }"
      item-key="id"
      handle=".three-dot-menu"
      :animation="200"
      ghost-class="ghost"
      chosen-class="chosen"
      drag-class="drag"
      @start="onDragStart"
      @end="onDragEnd"
      @change="onDragChange"
    >
      <template #item="{ element: block, index }">
        <div
          :key="block.id"
          :ref="(el) => (blockRefs[block.id] = el)"
          class="row justify-center draggable-item"
          :class="{ 'is-dragging': isDragging }"
        >
          <div class="col-auto">
            <div
              v-if="evaluateFormStore.isSectionBlock(index) && evaluateFormStore.totalSections > 1"
              class="col-12 section-container"
            >
              <div class="section-tab">
                ส่วนที่ {{ evaluateFormStore.getSectionNumber(index) }} จาก
                {{ evaluateFormStore.totalSections }}
              </div>
            </div>
            <div class="block-container">
              <div class="block-content full-width">
                <template v-if="block.type === 'HEADER'">
                  <HeaderBlock
                    :itemBlock="block"
                    :index="index"
                    :type="props.type"
                    class="evaluate-item"
                    :class="{
                      'no-top-left-radius':
                        evaluateFormStore.isSectionBlock(index) &&
                        evaluateFormStore.totalSections > 1,
                    }"
                    @focus-fab="handleFocusFab(block.id)"
                    @duplicate="() => handleDuplicateHeaderBlock(block, index)"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else-if="block.type === 'IMAGE'">
                  <ImageBlock
                    :item-block="block"
                    class="evaluate-item"
                    @focus-fab="handleFocusFab(block.id)"
                    @duplicate="() => handleDuplicateBlock(block, index)"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else>
                  <ItemBlockProvider :block-id="block.id" :item-block="block">
                    <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      class="evaluate-item"
                      @focus-fab="handleFocusFab(block.id)"
                      @duplicate="() => handleDuplicateBlock(block, index)"
                      @delete="() => onClickDeleteBlock(block, index)"
                      @update:question="handleQuestionUpdate"
                      @update:option="handleOptionUpdate"
                      @update:is-required="handleIsRequiredUpdate"
                    />
                  </ItemBlockProvider>
                </template>
              </div>
            </div>
          </div>

          <div class="col-auto fixed-fab-col">
            <FloatActionBtnForBlock
              v-show="evaluateFormStore.selectedBlockId === `block-${block.id}` && !isDragging"
              :disabled="isCreatingBlock"
              :type="props.type"
              @add="() => handleAddBlockAfter(index)"
              @add-text="() => handleAddHeaderAfter(index)"
              @add-section="() => handleAddSection()"
              @add-image="(payload) => handleAddImageBlock(index, payload)"
              @image-uploaded="() => handleImageUploaded()"
            />
          </div>
        </div>
      </template>
    </draggable>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, computed, type ComponentPublicInstance } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import type { ItemBlock, Option } from 'src/types/models';
import HeaderBlock from './HeaderBlock.vue';
import ItemBlockComponent from './ItemBlockComponent.vue';
import FloatActionBtnForBlock from './FloatActionBtnForBlock.vue';
import ItemBlockProvider from './ItemBlockProvider.vue';
import ImageBlock from './ImageBlock.vue';
import { defaultBlocks } from 'src/data/defaultBlocks';
import draggable from 'vuedraggable';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAssessmentStore } from 'src/stores/asm';
import { useGlobalStore } from 'src/stores/global';
import { getCurrentSection, copyBlockContentWithBackendPersistence } from 'src/utils/block_helper';

const props = defineProps<{
  blocks: ItemBlock[];
  type: 'quiz' | 'evaluate';
  assessmentId?: number | null;
}>();

defineOptions({
  name: 'block-creator',
});
// Use the new block creator evaluateFormStore
const evaluateFormStore = useBlockCreatorStore();
// evaluateFormStore is now integrated into evaluateFormStore (useBlockCreatorStore)
const asm = useAssessmentStore();
const globalStore = useGlobalStore();

// Initialize assessment service based on type
const assessmentService = new AssessmentService(props.type);

// State management
const isDragging = ref(false);
const isCreatingBlock = ref(false);

// FAB positioning state to prevent conflicts
const fabPositionLock = ref(false);
const pendingFabPosition = ref<number | null>(null);
const blockCreationInProgress = ref(false);
const targetBlockId = ref<number | null>(null);

// Draggable blocks computed property
const draggableBlocks = computed({
  get: () => evaluateFormStore.blocks,
  set: (newBlocks: ItemBlock[]) => {
    evaluateFormStore.updateBlocksOrder(newBlocks);
  },
});

// Function to initialize blocks based on current data
const initializeBlocksFromData = async () => {
  let blocksToUse: ItemBlock[] = [];

  if (props.type === 'evaluate') {
    // For evaluate type, get blocks from the block creator evaluateFormStore
    const assessmentBlocks = evaluateFormStore.currentAssessment?.itemBlocks;
    if (assessmentBlocks && assessmentBlocks.length > 0) {
      blocksToUse = assessmentBlocks;
    } else if (props.blocks && props.blocks.length > 0) {
      blocksToUse = props.blocks;
    } else {
      // Only use default blocks as last resort for evaluate type
      blocksToUse = defaultBlocks;
    }
  } else {
    // For quiz type, use props or default blocks
    blocksToUse = props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks;
  }

  evaluateFormStore.initializeBlocks(blocksToUse);

  // Initialize with first block selected
  if (evaluateFormStore.blocks.length > 0) {
    evaluateFormStore.selectedBlockId = `block-${evaluateFormStore.blocks[0]!.id}`;
    await nextTick();
    scrollToTarget();
  }
};

onMounted(async () => {
  await initializeBlocksFromData();
});

// Watch for changes in the block creator evaluateFormStore's current assessment
// This ensures that when data is fetched from backend, the blocks are re-initialized
watch(
  () => evaluateFormStore.currentAssessment,
  async (newAssessment) => {
    if (props.type === 'evaluate' && newAssessment?.itemBlocks) {
      await initializeBlocksFromData();
    }
  },
  { deep: true, immediate: false },
);

// Watch for changes in globalIsRequired to update all itemBlocks reactively
watch(
  () => evaluateFormStore.currentAssessment?.globalIsRequired,
  (newGlobalIsRequired, oldGlobalIsRequired) => {
    console.log('🔍 BlockCreator watcher triggered:', {
      type: props.type,
      newValue: newGlobalIsRequired,
      oldValue: oldGlobalIsRequired,
      hasAssessment: !!evaluateFormStore.currentAssessment,
      blocksCount: evaluateFormStore.blocks.length,
    });

    if (props.type === 'evaluate' && newGlobalIsRequired !== undefined) {
      // Update all non-header and non-image blocks in the local evaluateFormStore using proper evaluateFormStore methods
      let updatedCount = 0;
      evaluateFormStore.blocks.forEach((block, index) => {
        if (block.type !== 'HEADER' && block.type !== 'IMAGE') {
          const currentValue = block.isRequired ?? false;
          console.log(
            `📝 Updating block ${block.id} (index ${index}) isRequired: ${currentValue} → ${newGlobalIsRequired}`,
          );

          // Create updated block object with proper boolean value
          const updatedBlock = {
            ...block,
            isRequired: Boolean(newGlobalIsRequired),
          };

          // Use evaluateFormStore's updateBlock method to ensure reactivity
          evaluateFormStore.updateBlock(updatedBlock, index);
          updatedCount++;
        }
      });

      console.log(
        `✅ Updated ${updatedCount} blocks in local evaluateFormStore to match global isRequired setting`,
      );
    }
  },
  { immediate: false },
);

// Utility functions
// Note: IDs are now generated by the backend, so we don't need nextId for new blocks

// Block refs for scrolling/focus
async function setFabAndScroll(id: number) {
  // Use immediate positioning to prevent conflicts during block creation
  setFabPosition(id, true);

  // Wait for DOM updates
  await nextTick();
  await nextTick();

  // Scroll to the target block
  scrollToTarget();
}

function scrollToTarget() {
  if (!evaluateFormStore.selectedBlockId) return;
  const id = Number(evaluateFormStore.selectedBlockId.split('-')[1]);
  const el = blockRefs[id];
  if (el && 'scrollIntoView' in el) {
    (el as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

// Debounced FAB positioning to prevent conflicts and jumping
let fabPositionTimeout: NodeJS.Timeout | null = null;

const setFabPosition = (blockId: number, immediate = false) => {
  // If we're in the middle of creating a block, prioritize that operation
  if (isCreatingBlock.value && !immediate) {
    pendingFabPosition.value = blockId;
    return;
  }

  // Clear any pending timeout
  if (fabPositionTimeout) {
    clearTimeout(fabPositionTimeout);
  }

  if (immediate) {
    // Immediate positioning for critical operations (like new block creation)
    fabPositionLock.value = true;
    evaluateFormStore.selectedBlockId = `block-${blockId}`;

    // Release lock after a brief delay
    setTimeout(() => {
      fabPositionLock.value = false;
      // Apply any pending position change
      if (pendingFabPosition.value && pendingFabPosition.value !== blockId) {
        setFabPosition(pendingFabPosition.value, false);
        pendingFabPosition.value = null;
      }
    }, 200);
  } else {
    // Debounced positioning for regular interactions
    fabPositionTimeout = setTimeout(() => {
      if (!fabPositionLock.value) {
        evaluateFormStore.selectedBlockId = `block-${blockId}`;
      } else {
        // Store as pending if locked
        pendingFabPosition.value = blockId;
      }
    }, 50); // Short delay to prevent rapid changes
  }
};

// AGGRESSIVE FAB focus handler that completely blocks unwanted events
const handleFocusFab = (blockId: number) => {
  // ABSOLUTE BLOCK: During block creation, REJECT ALL events except target
  if (blockCreationInProgress.value) {
    if (targetBlockId.value && blockId !== targetBlockId.value) {
      console.log(
        `🚫 ABSOLUTELY BLOCKING focus event from block ${blockId} during creation of block ${targetBlockId.value}`,
      );
      // Force back to correct position immediately
      evaluateFormStore.selectedBlockId = `block-${targetBlockId.value}`;
      return;
    }
  }

  // ABSOLUTE BLOCK: Don't allow ANY repositioning if locked
  if (fabPositionLock.value) {
    console.log(`🚫 FAB is LOCKED - rejecting focus event from block ${blockId}`);
    return;
  }

  // Only allow positioning for legitimate user interactions
  setFabPosition(blockId, false);
};

// Handle question updates from ItemBlockComponent
const handleQuestionUpdate = (updateData: {
  questionId?: number;
  questionText?: string;
  itemBlockId: number;
  updatedQuestion?: object;
  updatedBlock?: ItemBlock;
  typeChanged?: boolean;
}) => {
  // AGGRESSIVE PROTECTION: Lock FAB during updates to prevent jumping
  const protectFabDuringUpdate = (blockId: number) => {
    blockCreationInProgress.value = true;
    targetBlockId.value = blockId;
    fabPositionLock.value = true;
    evaluateFormStore.selectedBlockId = `block-${blockId}`;

    // Release protection after update is complete
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      evaluateFormStore.selectedBlockId = `block-${blockId}`;
    }, 200);
  };

  // Handle type changes
  if (updateData.typeChanged && updateData.updatedBlock) {
    console.log('🔄 Handling ItemBlock type change:', {
      blockId: updateData.itemBlockId,
      newType: updateData.updatedBlock.type,
    });

    // PROTECT FAB BEFORE DOM UPDATES
    protectFabDuringUpdate(updateData.itemBlockId);

    // Update the block type in the local evaluateFormStore
    const blockIndex = evaluateFormStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );
    if (blockIndex !== -1) {
      // Replace the entire block with the updated one from backend
      evaluateFormStore.blocks[blockIndex] = updateData.updatedBlock;
    }

    // Update the block creator evaluateFormStore if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        // Replace the entire block with the updated one from backend
        evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex] =
          updateData.updatedBlock;
      }
    }
    return;
  }

  // Handle question text updates (existing logic)
  if (updateData.questionId && updateData.questionText !== undefined) {
    // PROTECT FAB BEFORE DOM UPDATES
    protectFabDuringUpdate(updateData.itemBlockId);

    // Update the question in the local evaluateFormStore
    const blockIndex = evaluateFormStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );
    if (blockIndex !== -1) {
      const block = evaluateFormStore.blocks[blockIndex];
      if (block?.questions && block.questions.length > 0) {
        const questionIndex = block.questions.findIndex((q) => q.id === updateData.questionId);
        if (questionIndex !== -1 && block.questions[questionIndex]) {
          // Update the question text in the evaluateFormStore
          block.questions[questionIndex].questionText = updateData.questionText;
        }
      }
    }

    // Update the block creator evaluateFormStore if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock?.questions && assessmentBlock.questions.length > 0) {
          const questionIndex = assessmentBlock.questions.findIndex(
            (q) => q.id === updateData.questionId,
          );
          if (questionIndex !== -1 && assessmentBlock.questions[questionIndex]) {
            // Update the question text in the block creator evaluateFormStore
            assessmentBlock.questions[questionIndex].questionText = updateData.questionText;
          }
        }
      }
    }
  }
};

// Handle option updates from ItemBlockComponent
const handleOptionUpdate = (updateData: {
  action: 'created' | 'updated';
  itemBlockId: number;
  option?: Option;
  optionId?: number;
  updateData?: { index: number; option: Option };
}) => {
  // AGGRESSIVE PROTECTION: Lock FAB during option updates to prevent jumping
  const protectFabDuringUpdate = (blockId: number) => {
    blockCreationInProgress.value = true;
    targetBlockId.value = blockId;
    fabPositionLock.value = true;
    evaluateFormStore.selectedBlockId = `block-${blockId}`;

    // Release protection after update is complete
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      evaluateFormStore.selectedBlockId = `block-${blockId}`;
    }, 200);
  };

  // PROTECT FAB BEFORE ANY DOM UPDATES
  protectFabDuringUpdate(updateData.itemBlockId);

  // Find the block in the local evaluateFormStore
  const blockIndex = evaluateFormStore.blocks.findIndex(
    (block) => block.id === updateData.itemBlockId,
  );
  if (blockIndex === -1) {
    console.error('❌ Block not found for option update:', updateData.itemBlockId);
    return;
  }

  const block = evaluateFormStore.blocks[blockIndex];
  if (!block) {
    console.error('❌ Block is undefined:', updateData.itemBlockId);
    return;
  }

  // Handle option creation
  if (updateData.action === 'created' && updateData.option) {
    // Initialize options array if it doesn't exist
    if (!block.options) {
      block.options = [];
    }

    // Add the new option to the block
    block.options.push(updateData.option);

    // Update the evaluate form evaluateFormStore if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock) {
          // Initialize options array if it doesn't exist
          if (!assessmentBlock.options) {
            assessmentBlock.options = [];
          }
          // Add the new option to the assessment block
          assessmentBlock.options.push(updateData.option);
        }
      }
    }
  }

  // Handle option updates
  if (updateData.action === 'updated' && updateData.optionId && updateData.updateData) {
    const { index, option } = updateData.updateData;

    // Update the option in the local evaluateFormStore
    if (block.options && block.options[index]) {
      block.options[index] = option;
    }

    // Update the evaluate form evaluateFormStore if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock?.options && assessmentBlock.options[index]) {
          // Update the option in the assessment block
          assessmentBlock.options[index] = option;
        }
      }
    }
  }
};

// Handle isRequired updates from ItemBlockComponent
const handleIsRequiredUpdate = async (updateData: { itemBlockId: number; isRequired: boolean }) => {
  // AGGRESSIVE PROTECTION: Lock FAB during isRequired updates to prevent jumping
  const protectFabDuringUpdate = (blockId: number) => {
    blockCreationInProgress.value = true;
    targetBlockId.value = blockId;
    fabPositionLock.value = true;
    evaluateFormStore.selectedBlockId = `block-${blockId}`;

    // Release protection after update is complete
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      evaluateFormStore.selectedBlockId = `block-${blockId}`;
    }, 200);
  };

  // PROTECT FAB BEFORE ANY DOM UPDATES
  protectFabDuringUpdate(updateData.itemBlockId);

  // Find the block in the local evaluateFormStore
  const blockIndex = evaluateFormStore.blocks.findIndex(
    (block) => block.id === updateData.itemBlockId,
  );
  if (blockIndex === -1) {
    console.error('❌ Block not found for isRequired update:', updateData.itemBlockId);
    return;
  }

  const block = evaluateFormStore.blocks[blockIndex];
  if (!block) {
    console.error('❌ Block is undefined:', updateData.itemBlockId);
    return;
  }

  try {
    // Update the isRequired property in the local evaluateFormStore first (optimistic update)
    const updatedBlock = {
      ...block,
      isRequired: Boolean(updateData.isRequired),
    };
    evaluateFormStore.updateBlock(updatedBlock, blockIndex);

    // Update the evaluate form evaluateFormStore if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock) {
          // Update the isRequired property in the assessment block with proper boolean value
          assessmentBlock.isRequired = Boolean(updateData.isRequired);

          // Trigger reactivity by creating a new array reference
          // This ensures the watcher in EvaluateSettingView detects the change
          evaluateFormStore.currentAssessment.itemBlocks = [
            ...evaluateFormStore.currentAssessment.itemBlocks,
          ];
        }
      }
    }

    // Call the backend API to persist the change
    console.log(`🌐 Updating isRequired via API for block ${updateData.itemBlockId}...`);
    const apiUpdatedBlock = await assessmentService.updateBlock(updatedBlock);

    if (apiUpdatedBlock) {
      console.log(
        `✅ Successfully updated isRequired for block ${updateData.itemBlockId} to: ${updateData.isRequired}`,
      );

      // Update the local evaluateFormStore with the response from the API (in case there are any differences)
      evaluateFormStore.updateBlock(apiUpdatedBlock, blockIndex);

      // Update the evaluate form evaluateFormStore with the API response
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex] = apiUpdatedBlock;
          // Trigger reactivity
          evaluateFormStore.currentAssessment.itemBlocks = [
            ...evaluateFormStore.currentAssessment.itemBlocks,
          ];
        }
      }
    }
  } catch (error) {
    console.error(`❌ Failed to update isRequired for block ${updateData.itemBlockId}:`, error);

    // Revert the optimistic update on error
    evaluateFormStore.updateBlock(block, blockIndex);

    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = evaluateFormStore.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock =
          evaluateFormStore.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock) {
          assessmentBlock.isRequired = block.isRequired;
          // Trigger reactivity
          evaluateFormStore.currentAssessment.itemBlocks = [
            ...evaluateFormStore.currentAssessment.itemBlocks,
          ];
        }
      }
    }

    // Error notification removed
  }
};

// Block operations
const handleAddBlockAfter = async (index: number) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    return;
  }

  try {
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating new question...');

    // Enhanced ID validation using evaluateFormStore helpers
    const assessmentId = props.assessmentId || asm.currentAssessment?.id;

    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      return;
    }

    // Validate that we have proper ID structure
    const validation = evaluateFormStore.validateIds();
    if (!validation.valid) {
      console.warn('⚠️ ID validation failed before adding block:', validation.missing);
    }

    const globalIsRequired = evaluateFormStore.currentAssessment?.globalIsRequired ?? false;
    const currentSection = getCurrentSection(evaluateFormStore.blocks, index);

    const newBlockData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'RADIO' as const,
      isRequired: globalIsRequired,
    };

    // Call backend API to create the block
    const addedBlock = await assessmentService.createBlock(newBlockData);

    if (addedBlock) {
      // AGGRESSIVE APPROACH: Set FAB position IMMEDIATELY and lock it completely
      const newBlockId = addedBlock.id;

      // Step 1: Completely disable all FAB events during creation
      blockCreationInProgress.value = true;
      targetBlockId.value = newBlockId;
      fabPositionLock.value = true;

      // Step 2: Force FAB position BEFORE any DOM changes
      evaluateFormStore.selectedBlockId = `block-${newBlockId}`;

      // Step 3: Create aggressive watcher to override any position changes
      const forceCorrectPosition = () => {
        if (evaluateFormStore.selectedBlockId !== `block-${newBlockId}`) {
          console.log(
            `🔒 FORCING FAB back to ItemBlock ${newBlockId} from ${evaluateFormStore.selectedBlockId}`,
          );
          evaluateFormStore.selectedBlockId = `block-${newBlockId}`;
        }
      };

      // Step 4: Set up aggressive position enforcement
      const positionEnforcer = setInterval(forceCorrectPosition, 10); // Check every 10ms

      // Step 5: Add to local evaluateFormStore (this triggers the problematic reactivity)
      evaluateFormStore.addBlock(addedBlock, index);

      // Step 6: Update the block creator evaluateFormStore
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      // Step 7: Complete save operation
      globalStore.completeSaveOperation(true, 'Question created successfully');

      // Success notification removed

      // Step 9: Wait for DOM to settle, then scroll
      await nextTick();
      await nextTick();
      scrollToTarget();

      // Step 10: Clean up after extended period
      setTimeout(() => {
        clearInterval(positionEnforcer);
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        // Final enforcement
        evaluateFormStore.selectedBlockId = `block-${newBlockId}`;
        console.log(`✅ FAB creation complete for ItemBlock ${newBlockId}`);
      }, 1000); // Extended cleanup period
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create question');
    }
  } catch (error) {
    console.error('❌ Error creating new block:', error);
    globalStore.completeSaveOperation(false, 'Error creating question');
  } finally {
    isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

const handleAddHeaderAfter = async (index: number) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    return;
  }

  try {
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating new header...');

    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      console.error('❌ Assessment ID is required for creating header block');
      return;
    }

    const currentSection = getCurrentSection(evaluateFormStore.blocks, index);

    const newHeaderData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'HEADER' as const,
      isRequired: false,
    };

    // Call backend API to create the header block
    const addedBlock = await assessmentService.createBlock(newHeaderData);

    if (addedBlock) {
      // CRITICAL: Set target block ID to allow only this block to receive focus
      targetBlockId.value = addedBlock.id;

      // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
      fabPositionLock.value = true;
      evaluateFormStore.selectedBlockId = `block-${addedBlock.id}`;

      // Add to local evaluateFormStore with backend response data
      evaluateFormStore.addBlock(addedBlock, index);

      // Update the evaluate form evaluateFormStore if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the evaluateFormStore
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      // Complete save operation successfully
      globalStore.completeSaveOperation(true, 'Header created successfully');

      // Success notification removed

      // Set FAB position and scroll (FAB is already locked to correct position)
      await setFabAndScroll(addedBlock.id);

      // Release all locks after DOM is stable
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        // Final confirmation of FAB position
        evaluateFormStore.selectedBlockId = `block-${addedBlock.id}`;
      }, 800); // Longer delay to ensure complete DOM stability
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create header');
      console.error('❌ Failed to create header block - no response from backend');
    }
  } catch (error) {
    globalStore.completeSaveOperation(false, 'Error creating header');
    console.error('❌ Error creating header block:', error);
    console.error('Error context:', {
      assessmentId: props.assessmentId || evaluateFormStore.getAssessmentId(),
      currentAssessment: !!evaluateFormStore.currentAssessment,
      blockIndex: index,
    });
  } finally {
    isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

const handleAddSection = async () => {
  // Prevent multiple simultaneous section creation
  if (isCreatingBlock.value) {
    return;
  }

  try {
    isCreatingBlock.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating new section...');

    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      console.error('❌ Assessment ID is required for creating section');
      return;
    }

    // Validate that we have proper ID structure
    const validation = evaluateFormStore.validateIds();
    if (!validation.valid) {
      console.warn('⚠️ ID validation failed before adding section:', validation.missing);
    }

    const globalIsRequired = evaluateFormStore.currentAssessment?.globalIsRequired ?? false;

    // Calculate the new section number
    const newSectionNumber = evaluateFormStore.totalSections + 1;

    // Calculate the next sequence numbers based on the current blocks length
    const nextHeaderSequence = evaluateFormStore.blocks.length + 1;
    const nextItemSequence = evaluateFormStore.blocks.length + 2;

    // Create header block data
    const headerBlockData = {
      assessmentId: assessmentId,
      sequence: nextHeaderSequence,
      section: newSectionNumber,
      type: 'HEADER' as const,
      isRequired: false,
    };

    // Create item block data
    const itemBlockData = {
      assessmentId: assessmentId,
      sequence: nextItemSequence,
      section: newSectionNumber,
      type: 'RADIO' as const,
      isRequired: globalIsRequired,
    };

    console.log('📝 Creating new section with header and item blocks:', {
      headerBlockData,
      itemBlockData,
    });

    // Create header block first
    const createdHeaderBlock = await assessmentService.createBlock(headerBlockData);
    if (!createdHeaderBlock) {
      globalStore.completeSaveOperation(false, 'Failed to create section header');
      console.error('❌ Failed to create header block for new section');
      return;
    }

    // Create item block second
    const createdItemBlock = await assessmentService.createBlock(itemBlockData);
    if (!createdItemBlock) {
      globalStore.completeSaveOperation(false, 'Failed to create section question');
      console.error('❌ Failed to create item block for new section');
      return;
    }

    console.log('✅ Section blocks successfully created:', {
      headerBlock: createdHeaderBlock,
      itemBlock: createdItemBlock,
    });

    // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
    fabPositionLock.value = true;
    evaluateFormStore.selectedBlockId = `block-${createdItemBlock.id}`;

    // Append the blocks to the end of the blocks array (local evaluateFormStore)
    evaluateFormStore.appendBlock(createdHeaderBlock);
    evaluateFormStore.appendBlock(createdItemBlock);

    // Update the evaluate form evaluateFormStore if this is an evaluate type
    if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
      // Add the new blocks to the current assessment in the evaluateFormStore
      const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
      evaluateFormStore.currentAssessment.itemBlocks = [
        ...currentBlocks,
        createdHeaderBlock,
        createdItemBlock,
      ];
    }

    // Complete save operation successfully
    globalStore.completeSaveOperation(true, 'Section created successfully');

    // Show success notification

    // Focus on the new ItemBlock (FAB is already locked to correct position)
    await setFabAndScroll(createdItemBlock.id);

    // Release lock after DOM is stable
    setTimeout(() => {
      fabPositionLock.value = false;
      // Ensure FAB is still positioned correctly
      evaluateFormStore.selectedBlockId = `block-${createdItemBlock.id}`;
    }, 500);
  } catch (error) {
    globalStore.completeSaveOperation(false, 'Error creating section');
    console.error('❌ Error creating new section:', error);
    console.error('Error context:', {
      assessmentId: props.assessmentId || evaluateFormStore.getAssessmentId(),
      currentAssessment: !!evaluateFormStore.currentAssessment,
      totalSections: evaluateFormStore.totalSections,
    });

    // Show error notification
  } finally {
    isCreatingBlock.value = false;
  }
};

const handleDuplicateHeaderBlock = async (source: ItemBlock, index: number) => {
  if (source.type !== 'HEADER') return;

  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    return;
  }

  try {
    isCreatingBlock.value = true;

    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      console.error('❌ Assessment ID is required for duplicating header block');
      return;
    }

    const currentSection = getCurrentSection(evaluateFormStore.blocks, index);

    // Create basic block data (backend will create headerBody automatically)
    const duplicateHeaderData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'HEADER' as const,
      isRequired: source.isRequired,
    };

    // Call backend API to create the duplicated header block
    const duplicatedBlock = await assessmentService.createBlock(duplicateHeaderData);

    if (duplicatedBlock) {
      // Copy header body content if source has headerBody data
      if (source.headerBody && duplicatedBlock.headerBody) {
        try {
          // Use the headerBodyService to update the duplicated block's header body
          const headerBodyService = new (
            await import('src/services/asm/headerBodyService')
          ).HeaderBodyService();
          await headerBodyService.update(duplicatedBlock.headerBody.id, {
            title: source.headerBody.title || '',
            description: source.headerBody.description || '',
            itemBlockId: duplicatedBlock.id,
          });
        } catch (error) {
          console.warn('⚠️ Failed to copy header body content:', error);
          // Continue with the process even if header body copying fails
        }
      }

      // Add to local evaluateFormStore with backend response data
      evaluateFormStore.addBlock(duplicatedBlock, index);

      // Update the evaluate form evaluateFormStore if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the evaluateFormStore
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, duplicatedBlock];
      }

      // Show success notification

      await setFabAndScroll(duplicatedBlock.id);
    } else {
      console.error('❌ Failed to duplicate header block - no response from backend');
    }
  } catch (error) {
    console.error('❌ Error duplicating header block:', error);
  } finally {
    isCreatingBlock.value = false;
  }
};

const handleDuplicateBlock = async (source: ItemBlock, index: number) => {
  if (source.type === 'HEADER' || source.type === 'IMAGE') return;

  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    return;
  }

  try {
    isCreatingBlock.value = true;
    console.log('🚀 Starting block duplication process...', {
      sourceType: source.type,
      sourceId: source.id,
    });

    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      console.error('❌ Assessment ID is required for duplicating block');
      return;
    }

    const currentSection = getCurrentSection(evaluateFormStore.blocks, index);

    // Get the current global isRequired state for new blocks
    const globalIsRequired = evaluateFormStore.currentAssessment?.globalIsRequired ?? false;

    // Create basic block data (backend will create default questions/options)
    const duplicateBlockData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: source.type,
      // Use global isRequired state for new blocks (duplicateBlock is only used for question blocks)
      isRequired: globalIsRequired,
    };

    // Call backend API to create the duplicated block
    const duplicatedBlock = await assessmentService.createBlock(duplicateBlockData);

    if (duplicatedBlock) {
      // Copy content from source block to duplicated block with backend persistence
      const duplicatedBlockWithContent = await copyBlockContentWithBackendPersistence(
        source,
        duplicatedBlock,
      );

      // Add to local evaluateFormStore with backend response data and copied content
      evaluateFormStore.addBlock(duplicatedBlockWithContent, index);

      // Update the evaluate form evaluateFormStore if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the evaluateFormStore
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [
          ...currentBlocks,
          duplicatedBlockWithContent,
        ];
      }

      // Show success notification

      await setFabAndScroll(duplicatedBlockWithContent.id);
    } else {
      console.error('❌ Failed to duplicate block - no response from backend');
    }
  } catch (error) {
    console.error('❌ Error duplicating block:', error);
  } finally {
    isCreatingBlock.value = false;
  }
};

const onClickDeleteBlock = async (item: ItemBlock, index: number) => {
  // Enhanced pre-deletion validation
  if (!item.id) {
    console.error('❌ Cannot delete block: Missing item ID');
    return;
  }

  if (!item.assessmentId) {
    console.error('❌ Cannot delete block: Missing assessmentId');
    return;
  }

  try {
    // Enhanced validation for header blocks
    if (item.type === 'HEADER' && !item.headerBody) {
      return;
    }

    // Enhanced validation using block creator evaluateFormStore
    if (props.type === 'evaluate') {
      const deletionValidation = evaluateFormStore.validateBlockDeletion(item.id);

      if (!deletionValidation.canDelete) {
        return;
      }
    }

    // Delete the ItemBlock (this will cascade delete related entities)
    const deletedBlock = await assessmentService.deleteBlock(item);

    if (deletedBlock !== undefined) {
      // Perform UI cleanup
      await handleBlockDeletionCleanup(item, index);
    }
  } catch {
    // Error handling without notification
  }
};

// Separate function to handle UI cleanup after successful deletion
const handleBlockDeletionCleanup = async (item: ItemBlock, index: number) => {
  // Remove from local evaluateFormStore
  evaluateFormStore.deleteBlock(index);

  // Update the evaluate form evaluateFormStore if this is an evaluate type
  if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
    const beforeFilter = evaluateFormStore.currentAssessment.itemBlocks || [];

    // Remove the block from the current assessment in the evaluateFormStore
    evaluateFormStore.currentAssessment.itemBlocks = beforeFilter.filter(
      (block) => block.id !== item.id,
    );
  }

  // Handle focus after deletion - go to previous block (ItemBlock or HeaderBlock)
  if (evaluateFormStore.blocks.length > 0) {
    let targetIndex;

    // Try to go to the previous block (index - 1)
    if (index > 0) {
      targetIndex = index - 1;
    }
    // If we're deleting the first block, go to the new first block (index 0)
    else {
      targetIndex = 0;
    }

    // Make sure the target index is within bounds
    targetIndex = Math.min(targetIndex, evaluateFormStore.blocks.length - 1);

    const targetBlock = evaluateFormStore.blocks[targetIndex];
    if (targetBlock) {
      console.log(
        `📍 After deletion, moving FAB to previous block at index ${targetIndex} (ID: ${targetBlock.id}, Type: ${targetBlock.type})`,
      );

      // Use the aggressive positioning approach for deletion as well
      blockCreationInProgress.value = true;
      targetBlockId.value = targetBlock.id;
      fabPositionLock.value = true;

      evaluateFormStore.selectedBlockId = `block-${targetBlock.id}`;
      await setFabAndScroll(targetBlock.id);

      // Release locks after positioning
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        console.log(
          `✅ FAB positioned on previous block ${targetBlock.id} (${targetBlock.type}) after deletion`,
        );
      }, 300);
    }
  } else {
    evaluateFormStore.selectedBlockId = undefined;
  }
};

const handleAddImageBlock = async (
  index: number,
  payload: { callback: (id: number | null) => void },
) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    payload.callback(null);
    return;
  }

  try {
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating image block...');

    const assessmentId = props.assessmentId || evaluateFormStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      payload.callback(null);
      return;
    }

    const currentSection = getCurrentSection(evaluateFormStore.blocks, index);

    const newImageData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'IMAGE' as const,
      isRequired: false,
    };

    // Call backend API to create the image block (without image data)
    const addedBlock = await assessmentService.createBlock(newImageData);

    if (addedBlock) {
      // CRITICAL: Set target block ID to allow only this block to receive focus
      targetBlockId.value = addedBlock.id;

      // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
      fabPositionLock.value = true;
      evaluateFormStore.selectedBlockId = `block-${addedBlock.id}`;

      // Add to local evaluateFormStore with backend response data
      evaluateFormStore.addBlock(addedBlock, index);

      // Update the evaluate form evaluateFormStore if this is an evaluate type
      if (props.type === 'evaluate' && evaluateFormStore.currentAssessment) {
        // Add the new block to the current assessment in the evaluateFormStore
        const currentBlocks = evaluateFormStore.currentAssessment.itemBlocks || [];
        evaluateFormStore.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      // Complete save operation successfully
      globalStore.completeSaveOperation(true, 'Image block created successfully');

      // Set FAB position and scroll (FAB is already locked to correct position)
      await setFabAndScroll(addedBlock.id);

      // Release locks after DOM is stable
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        // Final confirmation of FAB position
        evaluateFormStore.selectedBlockId = `block-${addedBlock.id}`;
      }, 800);

      // Return the created block ID to the callback
      payload.callback(addedBlock.id);
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create image block');
      payload.callback(null);
    }
  } catch (error) {
    console.error('❌ Error creating image block:', error);
    globalStore.completeSaveOperation(false, 'Error creating image block');
    payload.callback(null);
  } finally {
    isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

const handleImageUploaded = async () => {
  // This function is called when the image upload is completed
  // We need to refresh the current assessment data to get the updated imageBody
  console.log('✅ Image uploaded successfully, refreshing assessment data...');

  try {
    if (props.type === 'evaluate' && props.assessmentId) {
      // Refresh the current assessment to get the updated imageBody data
      const updatedAssessment = await assessmentService.fetchOne(props.assessmentId);

      if (updatedAssessment && evaluateFormStore.currentAssessment) {
        // Update the current assessment in the store
        evaluateFormStore.currentAssessment = updatedAssessment;

        // Also update the local blocks array
        if (updatedAssessment.itemBlocks) {
          evaluateFormStore.initializeBlocks(updatedAssessment.itemBlocks);
        }

        console.log('✅ Assessment data refreshed successfully', {
          itemBlocks: updatedAssessment.itemBlocks?.length,
          imageBlocks: updatedAssessment.itemBlocks
            ?.filter((block) => block.type === 'IMAGE')
            .map((block) => ({
              id: block.id,
              imageText: block.imageBody?.imageText,
              imagePath: block.imageBody?.imagePath,
            })),
        });
      }
    }
  } catch (error) {
    console.error('❌ Failed to refresh assessment data after image upload:', error);
  }
};

// Drag and drop event handlers
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = () => {
  isDragging.value = false;
};

const onDragChange = () => {
  // Handle drag change events if needed
};

// AGGRESSIVE watcher to prevent unwanted FAB position changes
watch(
  () => evaluateFormStore.selectedBlockId,
  (newValue) => {
    // During block creation, aggressively enforce the target position
    if (blockCreationInProgress.value && targetBlockId.value) {
      const expectedId = `block-${targetBlockId.value}`;
      if (newValue !== expectedId) {
        console.log(`🔒 WATCHER: Forcing FAB back to ${expectedId} from ${newValue}`);
        // Use nextTick to avoid infinite loops
        void nextTick(() => {
          evaluateFormStore.selectedBlockId = expectedId;
        });
        return;
      }
    }

    // Normal scroll behavior for legitimate changes
    scrollToTarget();
  },
);

// Block refs for template
const blockRefs: Record<number, Element | ComponentPublicInstance | null> = new Proxy(
  {},
  {
    get(_target, id: string) {
      return evaluateFormStore.getBlockRef(Number(id));
    },
    set(_target, id: string, el) {
      evaluateFormStore.setBlockRef(Number(id), el);
      return true;
    },
  },
);
</script>

<style scoped>
.fixed-fab-col {
  width: 48px;
}

.section-container {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
  position: relative;
  left: 0;
}

.no-top-left-radius {
  border-top-left-radius: 0 !important;
}

/* Drag and Drop Styles */
.draggable-item {
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.draggable-item.is-dragging {
  opacity: 0.8;
}

.block-container {
  position: relative;
  width: 100%;
}

.block-content {
  flex: 1;
}

/* Drag states */
.ghost {
  opacity: 0.5;
  background: #f0f0f0;
  border: 2px dashed #ccc;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Flip animation for smooth transitions */
.flip-list-move,
.flip-list-enter-active,
.flip-list-leave-active {
  transition: all 0.3s ease;
}

.flip-list-enter-from,
.flip-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.flip-list-leave-active {
  position: absolute;
}
</style>
