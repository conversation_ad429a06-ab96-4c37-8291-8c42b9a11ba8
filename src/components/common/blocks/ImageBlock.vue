<script setup lang="ts">
import EditorTool from 'src/components/common/EditorTool.vue';
import { computed, ref, watch } from 'vue';
import type { ImageBody, ItemBlock } from 'src/types/models';
import type { CSSProperties } from 'vue';
import ItemBlockFooter from './ItemBlockFooter.vue';
import FloatImageBtn from '../FloatImageBtn.vue';
import { useGlobalStore } from 'src/stores/global';
import { ImageBodyService } from 'src/services/asm/imageBodyService';
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

const emit = defineEmits(['focus-fab', 'duplicate', 'delete', 'update:image']);
const globalStore = useGlobalStore();
const selectedFile = ref<File | null>(null);
const itemBlockId = ref(props.itemBlock.id || 0);
const imageId = ref(props.itemBlock.imageBody?.id || 0);

// Initialize imageText - treat empty string and null as undefined to show placeholder
const initialImageText = props.itemBlock.imageBody?.imageText;
const imageText = ref(
  initialImageText === '' || initialImageText === null ? undefined : initialImageText,
);
const lastSavedImageText = ref(imageText.value);

// Watch for changes in itemBlock props to update local state
watch(
  () => props.itemBlock.imageBody?.imageText,
  (newImageText) => {
    console.log('🔄 ImageBlock watcher triggered:', {
      newImageText,
      currentImageText: imageText.value,
      itemBlockId: props.itemBlock.id,
    });

    // Treat empty string and null as undefined to show placeholder
    const processedText = newImageText === '' || newImageText === null ? undefined : newImageText;
    if (processedText !== imageText.value) {
      console.log('📝 Updating imageText:', { from: imageText.value, to: processedText });
      imageText.value = processedText;
      lastSavedImageText.value = processedText;
    }
  },
  { immediate: true },
);

// Watch for changes in imageBody ID to update local state
watch(
  () => props.itemBlock.imageBody?.id,
  (newImageId) => {
    if (newImageId !== undefined) {
      imageId.value = newImageId;
    }
  },
  { immediate: true },
);

// Watch for changes in itemBlock ID to update local state
watch(
  () => props.itemBlock.id,
  (newItemBlockId) => {
    if (newItemBlockId !== undefined) {
      itemBlockId.value = newItemBlockId;
    }
  },
  { immediate: true },
);

const imgUrl = computed(() => props.itemBlock.imageBody?.imagePath || '');

const imageStyle = computed<CSSProperties>(() => {
  const body = props.itemBlock.imageBody;
  let width: string | undefined;
  let height: string | undefined;
  if (body) {
    if (typeof body.imageWidth === 'number' && body.imageWidth > 0) {
      width = `${body.imageWidth}px`;
    }
    if (typeof body.imageHeight === 'number' && body.imageHeight > 0) {
      height = `${body.imageHeight}px`;
    }
  }
  return {
    width: width ?? 'auto',
    height: height ?? 'auto',
    maxWidth: '800px',
    maxHeight: '500px',
    overflowX: 'auto',
    overflowY: 'auto',
  };
});
async function performSaveImageText() {
  const imageBody = props.itemBlock.imageBody;
  const isCreate = !imageBody?.id;

  // Convert undefined to empty string for comparison
  const currentText = imageText.value || '';
  const lastSavedText = lastSavedImageText.value || '';

  console.log('💾 performSaveImageText called:', {
    isCreate,
    currentText,
    lastSavedText,
    hasFile: !!selectedFile.value,
    imageBodyId: imageBody?.id,
    existingImagePath: imageBody?.imagePath,
  });

  if (currentText.trim() === lastSavedText.trim() && !selectedFile.value) {
    console.log('⏭️ Skipping save - no changes detected');
    return;
  }

  try {
    globalStore.startSaveOperation(isCreate ? 'Creating...' : 'Saving...');

    const service = new ImageBodyService();
    let updated: ImageBody;

    if (isCreate) {
      updated = await service.createImageBody(
        {
          itemBlockId: props.itemBlock.id,
          imageText: currentText, // Use converted text value
          id: itemBlockId.value,
        },
        selectedFile.value!,
      );
    } else {
      // Check if we're updating with a file or just text
      if (selectedFile.value) {
        // Update with new file
        updated = await service.updateImageBody(
          imageBody.id,
          {
            itemBlockId: props.itemBlock.id,
            imageText: currentText, // Use converted text value
            id: imageId.value,
          },
          selectedFile.value,
        );
      } else {
        // Text-only update to preserve existing image
        console.log('📝 Performing text-only update to preserve image', {
          imageBodyId: imageBody.id,
          currentText,
          existingImagePath: imageBody.imagePath,
        });
        updated = await service.updateImageTextOnly(
          imageBody.id,
          currentText,
          imageBody.imagePath || undefined,
        );
      }
    }

    // Handle backend response properly
    console.log('✅ Save successful, backend response:', {
      imageText: updated.imageText,
      imagePath: updated.imagePath,
      id: updated.id,
    });

    // Backend returns null for empty text, so handle both null and empty string
    const backendImageText = updated.imageText;
    if (backendImageText !== undefined && backendImageText !== null && backendImageText !== '') {
      // Backend returned actual text content
      console.log('📝 Setting imageText from backend:', backendImageText);
      imageText.value = backendImageText;
      lastSavedImageText.value = backendImageText;
    } else {
      // Backend returned null/undefined/empty, treat as undefined for UI to show placeholder
      console.log('🔄 Backend returned empty text, setting to undefined for placeholder');
      imageText.value = undefined;
      lastSavedImageText.value = undefined;
    }
    selectedFile.value = null; // เคลียร์ไฟล์หลังบันทึกเสร็จ

    globalStore.completeSaveOperation(
      true,
      isCreate ? 'Created successfully' : 'Saved successfully',
    );

    // if (isCreate) {
    //   emit('update:image', updated);
    // }
  } catch {
    globalStore.completeSaveOperation(false, isCreate ? 'Create failed' : 'Save failed');
  }
}

// Handle click on ImageBlock to focus FAB
const handleImageBlockClick = (event: Event) => {
  // Prevent focusing FAB if clicking on interactive elements
  const target = event.target as HTMLElement;

  // Check if the click is on an interactive element that should not trigger FAB focus
  if (
    target.closest('.q-btn') || // Any button
    target.closest('.q-input') || // Any input field
    target.closest('.q-editor') || // Editor tool
    target.closest('.pixel-image-position') || // FloatImageBtn
    target.closest('[contenteditable]') // Any contenteditable element
  ) {
    return;
  }

  // Emit focus-fab event to position the FAB on this ImageBlock
  emit('focus-fab', props.itemBlock.id);
};
</script>

<template>
  <q-card
    class="q-pa-lg image-block-container"
    style="min-height: 500px; max-height: 800px; cursor: pointer"
    @click="handleImageBlockClick"
  >
    <EditorTool
      class="q-mt-sm"
      label="ข้อความ..."
      :initialValue="imageText || ''"
      @update:content="(val) => (imageText = val || undefined)"
      @blur="performSaveImageText"
    />
    <q-card-section v-if="imgUrl.length > 0">
      <div class="row justify-center">
        <div class="image-container">
          <img :src="imgUrl" alt="image" :style="imageStyle" />
          <FloatImageBtn class="pixel-image-position" />
        </div>
      </div>
    </q-card-section>
    <q-separator></q-separator>
    <q-card-section style="max-height: 50px">
      <ItemBlockFooter label="ข้อความ" style="margin-top: -10px" @delete="$emit('delete')" />
    </q-card-section>
  </q-card>
</template>
<style scoped>
.pixel-image-position {
  position: absolute;
  top: -16px; /* Position at the very top of the image */
  left: -32px; /* Position at the very left of the image */
  z-index: 10; /* Ensure it's above the image */
  margin: 0px; /* Add a small margin for visual spacing from the edge */
  cursor: pointer; /* Show pointer cursor on hover */
}

/* Add a container class to ensure proper positioning context */
.image-container {
  position: relative;
  display: inline-block; /* Contain the image and overlay */
  margin: 0 auto; /* Center the container */
}

/* Style for the clickable ImageBlock container */
.image-block-container {
  transition: box-shadow 0.2s ease;
}

.image-block-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
