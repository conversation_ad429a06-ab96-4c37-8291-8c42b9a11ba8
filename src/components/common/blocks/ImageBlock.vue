<script setup lang="ts">
import EditorTool from 'src/components/common/EditorTool.vue';
import { computed, ref, watch } from 'vue';
import type { ImageBody, ItemBlock } from 'src/types/models';
import type { CSSProperties } from 'vue';
import ItemBlockFooter from './ItemBlockFooter.vue';
import FloatImageBtn from '../FloatImageBtn.vue';
import { useGlobalStore } from 'src/stores/global';
import { ImageBodyService } from 'src/services/asm/imageBodyService';
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

const emit = defineEmits(['focus-fab', 'duplicate', 'delete', 'update:image']);
const globalStore = useGlobalStore();
const selectedFile = ref<File | null>(null);
const itemBlockId = ref(props.itemBlock.id || 0);
const imageId = ref(props.itemBlock.imageBody?.id || 0);

// Initialize imageText - treat empty string and null as undefined to show placeholder
const initialImageText = props.itemBlock.imageBody?.imageText;
const imageText = ref(
  initialImageText === '' || initialImageText === null ? undefined : initialImageText,
);
const lastSavedImageText = ref(imageText.value);

// Watch for changes in itemBlock props to update local state
watch(
  () => props.itemBlock.imageBody?.imageText,
  (newImageText) => {
    console.log('🔄 ImageBlock watcher triggered:', {
      newImageText,
      currentImageText: imageText.value,
      itemBlockId: props.itemBlock.id,
    });

    // Treat empty string and null as undefined to show placeholder
    const processedText = newImageText === '' || newImageText === null ? undefined : newImageText;
    if (processedText !== imageText.value) {
      console.log('📝 Updating imageText:', { from: imageText.value, to: processedText });
      imageText.value = processedText;
      lastSavedImageText.value = processedText;
    }
  },
  { immediate: true },
);

// Watch for changes in imageBody ID to update local state
watch(
  () => props.itemBlock.imageBody?.id,
  (newImageId) => {
    if (newImageId !== undefined) {
      imageId.value = newImageId;
    }
  },
  { immediate: true },
);

// Watch for changes in itemBlock ID to update local state
watch(
  () => props.itemBlock.id,
  (newItemBlockId) => {
    if (newItemBlockId !== undefined) {
      itemBlockId.value = newItemBlockId;
    }
  },
  { immediate: true },
);

// Watch for changes in image dimensions to trigger container adaptation
watch(
  () => [props.itemBlock.imageBody?.imageWidth, props.itemBlock.imageBody?.imageHeight],
  ([newWidth, newHeight], [oldWidth, oldHeight]) => {
    if (newWidth !== oldWidth || newHeight !== oldHeight) {
      console.log('🔄 Image dimensions changed, container will adapt:', {
        from: { width: oldWidth, height: oldHeight },
        to: { width: newWidth, height: newHeight },
        itemBlockId: props.itemBlock.id,
      });
      // The containerStyle computed property will automatically recalculate
      // due to its reactive dependencies on imageWidth and imageHeight
    }
  },
  { immediate: false },
);

const imgUrl = computed(() => props.itemBlock.imageBody?.imagePath || '');

const imageStyle = computed<CSSProperties>(() => {
  const body = props.itemBlock.imageBody;

  // If no dimensions are available, use auto sizing with reasonable constraints
  if (
    !body ||
    !body.imageWidth ||
    !body.imageHeight ||
    body.imageWidth <= 0 ||
    body.imageHeight <= 0
  ) {
    return {
      width: 'auto',
      height: 'auto',
      maxWidth: '100%',
      maxHeight: '600px',
      objectFit: 'contain',
    };
  }

  const originalWidth = body.imageWidth;
  const originalHeight = body.imageHeight;

  // Define maximum container constraints for responsive design
  const maxContainerWidth = 1200; // Maximum width for very large images
  const maxContainerHeight = 800; // Maximum height for very tall images
  const minDisplayWidth = 200; // Minimum width for very small images
  const minDisplayHeight = 150; // Minimum height for very small images

  // Calculate aspect ratio
  const aspectRatio = originalWidth / originalHeight;

  let finalWidth = originalWidth;
  let finalHeight = originalHeight;

  // Handle very large images - scale down proportionally
  if (originalWidth > maxContainerWidth || originalHeight > maxContainerHeight) {
    const widthScale = maxContainerWidth / originalWidth;
    const heightScale = maxContainerHeight / originalHeight;
    const scale = Math.min(widthScale, heightScale);

    finalWidth = Math.round(originalWidth * scale);
    finalHeight = Math.round(originalHeight * scale);

    console.log('📏 Scaling down large image:', {
      original: { width: originalWidth, height: originalHeight },
      scaled: { width: finalWidth, height: finalHeight },
      scale: scale,
      aspectRatio: aspectRatio,
    });
  }
  // Handle very small images - ensure minimum usable size
  else if (originalWidth < minDisplayWidth || originalHeight < minDisplayHeight) {
    const widthScale = minDisplayWidth / originalWidth;
    const heightScale = minDisplayHeight / originalHeight;
    const scale = Math.max(widthScale, heightScale);

    // Only scale up if the image is significantly smaller than minimum
    if (scale > 1.5) {
      finalWidth = Math.round(originalWidth * scale);
      finalHeight = Math.round(originalHeight * scale);

      console.log('📏 Scaling up small image:', {
        original: { width: originalWidth, height: originalHeight },
        scaled: { width: finalWidth, height: finalHeight },
        scale: scale,
        aspectRatio: aspectRatio,
      });
    }
  }

  console.log('🎯 Final image dimensions:', {
    original: { width: originalWidth, height: originalHeight },
    display: { width: finalWidth, height: finalHeight },
    aspectRatio: aspectRatio,
  });

  return {
    width: `${finalWidth}px`,
    height: `${finalHeight}px`,
    objectFit: 'contain' as const,
    display: 'block',
    margin: '0 auto',
    // Ensure image doesn't exceed container bounds on smaller screens
    maxWidth: '100%',
    maxHeight: '80vh',
  };
});

// Computed style for the container to adapt to image content
const containerStyle = computed<CSSProperties>(() => {
  const body = props.itemBlock.imageBody;

  // Base container styles
  const baseStyle: CSSProperties = {
    cursor: 'pointer',
    minHeight: '300px', // Reduced minimum height for better adaptation
    transition: 'all 0.3s ease',
  };

  // If we have image dimensions, adapt container accordingly
  if (body && body.imageWidth && body.imageHeight && body.imageWidth > 0 && body.imageHeight > 0) {
    const originalWidth = body.imageWidth;
    const originalHeight = body.imageHeight;

    // Calculate a reasonable container height based on image content
    // Add padding for text editor and footer
    const editorHeight = 80; // Approximate height for EditorTool
    const footerHeight = 60; // Approximate height for ItemBlockFooter
    const padding = 64; // q-pa-lg padding (32px * 2)
    const extraSpace = 40; // Additional breathing room

    // Determine display dimensions (same logic as imageStyle)
    const maxImageWidth = 1200;
    const maxImageHeight = 800;

    let displayHeight = originalHeight;

    // Scale down large images
    if (originalWidth > maxImageWidth || originalHeight > maxImageHeight) {
      const widthScale = maxImageWidth / originalWidth;
      const heightScale = maxImageHeight / originalHeight;
      const scale = Math.min(widthScale, heightScale);
      displayHeight = Math.round(originalHeight * scale);
    }

    // Calculate total container height
    const totalHeight = displayHeight + editorHeight + footerHeight + padding + extraSpace;

    // Set reasonable bounds for container
    const minContainerHeight = 300;
    const maxContainerHeight = 900;
    const finalHeight = Math.max(minContainerHeight, Math.min(maxContainerHeight, totalHeight));

    baseStyle.minHeight = `${finalHeight}px`;
    baseStyle.maxHeight = `${finalHeight + 100}px`; // Allow some flexibility

    console.log('📦 Container adapted to image:', {
      imageSize: { width: originalWidth, height: originalHeight },
      displayHeight: displayHeight,
      containerHeight: finalHeight,
    });
  }

  return baseStyle;
});

async function performSaveImageText() {
  const imageBody = props.itemBlock.imageBody;
  const isCreate = !imageBody?.id;

  // Convert undefined to empty string for comparison
  const currentText = imageText.value || '';
  const lastSavedText = lastSavedImageText.value || '';

  console.log('💾 performSaveImageText called:', {
    isCreate,
    currentText,
    lastSavedText,
    hasFile: !!selectedFile.value,
    imageBodyId: imageBody?.id,
    existingImagePath: imageBody?.imagePath,
  });

  if (currentText.trim() === lastSavedText.trim() && !selectedFile.value) {
    console.log('⏭️ Skipping save - no changes detected');
    return;
  }

  try {
    globalStore.startSaveOperation(isCreate ? 'Creating...' : 'Saving...');

    const service = new ImageBodyService();
    let updated: ImageBody;

    if (isCreate) {
      updated = await service.createImageBody(
        {
          itemBlockId: props.itemBlock.id,
          imageText: currentText, // Use converted text value
          id: itemBlockId.value,
        },
        selectedFile.value!,
      );
    } else {
      // Check if we're updating with a file or just text
      if (selectedFile.value) {
        // Update with new file
        updated = await service.updateImageBody(
          imageBody.id,
          {
            itemBlockId: props.itemBlock.id,
            imageText: currentText, // Use converted text value
            id: imageId.value,
          },
          selectedFile.value,
        );
      } else {
        // Text-only update to preserve existing image
        console.log('📝 Performing text-only update to preserve image', {
          imageBodyId: imageBody.id,
          currentText,
          existingImagePath: imageBody.imagePath,
        });
        updated = await service.updateImageTextOnly(
          imageBody.id,
          currentText,
          imageBody.imagePath || undefined,
        );
      }
    }

    // Handle backend response properly
    console.log('✅ Save successful, backend response:', {
      imageText: updated.imageText,
      imagePath: updated.imagePath,
      id: updated.id,
    });

    // Backend returns null for empty text, so handle both null and empty string
    const backendImageText = updated.imageText;
    if (backendImageText !== undefined && backendImageText !== null && backendImageText !== '') {
      // Backend returned actual text content
      console.log('📝 Setting imageText from backend:', backendImageText);
      imageText.value = backendImageText;
      lastSavedImageText.value = backendImageText;
    } else {
      // Backend returned null/undefined/empty, treat as undefined for UI to show placeholder
      console.log('🔄 Backend returned empty text, setting to undefined for placeholder');
      imageText.value = undefined;
      lastSavedImageText.value = undefined;
    }
    selectedFile.value = null; // เคลียร์ไฟล์หลังบันทึกเสร็จ

    globalStore.completeSaveOperation(
      true,
      isCreate ? 'Created successfully' : 'Saved successfully',
    );

    // if (isCreate) {
    //   emit('update:image', updated);
    // }
  } catch {
    globalStore.completeSaveOperation(false, isCreate ? 'Create failed' : 'Save failed');
  }
}

// Handle click on ImageBlock to focus FAB
const handleImageBlockClick = (event: Event) => {
  // Prevent focusing FAB if clicking on interactive elements
  const target = event.target as HTMLElement;

  // Check if the click is on an interactive element that should not trigger FAB focus
  if (
    target.closest('.q-btn') || // Any button
    target.closest('.q-input') || // Any input field
    target.closest('.q-editor') || // Editor tool
    target.closest('.pixel-image-position') || // FloatImageBtn
    target.closest('[contenteditable]') // Any contenteditable element
  ) {
    return;
  }

  // Emit focus-fab event to position the FAB on this ImageBlock
  emit('focus-fab', props.itemBlock.id);
};

// Handle dimensions updated from FloatImageBtn
const handleDimensionsUpdated = (dimensions: { width: number; height: number }) => {
  console.log('🎯 Image dimensions updated from FloatImageBtn:', {
    itemBlockId: props.itemBlock.id,
    imageBodyId: props.itemBlock.imageBody?.id,
    newDimensions: dimensions,
    previousDimensions: {
      width: props.itemBlock.imageBody?.imageWidth,
      height: props.itemBlock.imageBody?.imageHeight,
    },
  });

  // The dimensions are already updated in the backend by FloatImageBtn
  // This event handler can be used for additional UI updates if needed
  // For example, triggering a refresh of the parent component or emitting to grandparent

  // Emit to parent component if needed
  emit('update:image', {
    itemBlockId: props.itemBlock.id,
    dimensions: dimensions,
  });
};
</script>

<template>
  <q-card
    class="q-pa-lg image-block-container"
    :style="containerStyle"
    @click="handleImageBlockClick"
  >
    <EditorTool
      class="q-mt-sm"
      label="ข้อความ..."
      :initialValue="imageText || ''"
      @update:content="(val) => (imageText = val || undefined)"
      @blur="performSaveImageText"
    />
    <q-card-section v-if="imgUrl.length > 0">
      <div class="row justify-center">
        <div class="image-container">
          <img :src="imgUrl" alt="image" :style="imageStyle" />
          <FloatImageBtn
            class="pixel-image-position"
            :item-block="props.itemBlock"
            @dimensions-updated="handleDimensionsUpdated"
          />
        </div>
      </div>
    </q-card-section>
    <q-separator></q-separator>
    <q-card-section style="max-height: 50px">
      <ItemBlockFooter label="ข้อความ" style="margin-top: -10px" @delete="$emit('delete')" />
    </q-card-section>
  </q-card>
</template>
<style scoped>
.pixel-image-position {
  position: absolute;
  top: -16px; /* Position at the very top of the image */
  left: -32px; /* Position at the very left of the image */
  z-index: 10; /* Ensure it's above the image */
  margin: 0px; /* Add a small margin for visual spacing from the edge */
  cursor: pointer; /* Show pointer cursor on hover */
}

/* Enhanced image container for responsive image display */
.image-container {
  position: relative;
  display: inline-block; /* Contain the image and overlay */
  margin: 0 auto; /* Center the container */
  max-width: 100%; /* Ensure container doesn't exceed parent width */
  overflow: visible; /* Allow FloatImageBtn to be visible outside bounds */
}

/* Enhanced styles for the clickable ImageBlock container */
.image-block-container {
  transition:
    box-shadow 0.2s ease,
    min-height 0.3s ease;
  overflow: hidden; /* Prevent content from overflowing */
  display: flex;
  flex-direction: column;
}

.image-block-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .image-block-container {
    min-height: 250px !important;
    max-height: 600px !important;
  }

  .image-container {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .image-block-container {
    min-height: 200px !important;
    max-height: 500px !important;
  }

  .image-container {
    max-width: 90%;
  }
}
</style>
